import { Injectable, Logger } from '@nestjs/common';
import { TodoAttachmentRepository } from '../repositories/todo-attachment.repository';
import { TodoRepository } from '../repositories/todo.repository';
import { TodoAttachment } from '../entities/todo-attachment.entity';
import { CreateTodoAttachmentDto } from '../dto/todo-attachment/create-todo-attachment.dto';
import { CreateUploadUrlDto } from '../dto/todo-attachment/create-upload-url.dto';
import { UploadUrlResponseDto } from '../dto/todo-attachment/upload-url-response.dto';
import { ConfirmUploadDto } from '../dto/todo-attachment/confirm-upload.dto';
import { TodoAttachmentQueryDto } from '../dto/todo-attachment/todo-attachment-query.dto';
import { TodoAttachmentResponseDto } from '../dto/todo-attachment/todo-attachment-response.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common/exceptions/app.exception';
import { TODOLISTS_ERROR_CODES } from '../errors/todolists-error.code';
import { S3Service } from '@/shared/services/s3.service';
import { CdnService } from '@/shared/services/cdn.service';
import { MediaType, MediaTypeUtil } from '@shared/utils/file/media-type.util';
import { FileSizeEnum } from '@shared/utils/file/file-size.util';
import { TimeIntervalEnum } from '@shared/utils/time/time-interval.util';

/**
 * Service xử lý logic nghiệp vụ cho tệp đính kèm công việc
 */
@Injectable()
export class TodoAttachmentService {
  private readonly logger = new Logger(TodoAttachmentService.name);

  constructor(
    private readonly todoAttachmentRepository: TodoAttachmentRepository,
    private readonly todoRepository: TodoRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Chuyển đổi entity sang DTO
   * @param attachment Entity tệp đính kèm
   * @returns DTO tệp đính kèm
   */
  private async mapToDto(attachment: TodoAttachment): Promise<TodoAttachmentResponseDto> {
    // Tạo download URL từ CDN service nếu url là S3 key
    let downloadUrl: string = attachment.url;
    if (attachment.url && !attachment.url.startsWith('http')) {
      try {
        const generatedUrl = await this.cdnService.generateUrlView(
          attachment.url,
          TimeIntervalEnum.ONE_DAY,
        );
        // Kiểm tra nếu generateUrlView trả về null thì sử dụng URL gốc
        downloadUrl = generatedUrl || attachment.url;
      } catch (error) {
        this.logger.warn(`Không thể tạo download URL cho attachment ${attachment.id}: ${error.message}`);
        downloadUrl = attachment.url; // Fallback to original URL
      }
    }

    return {
      id: attachment.id,
      todoId: attachment.todoId,
      filename: attachment.filename,
      url: downloadUrl,
      contentType: attachment.contentType,
      size: attachment.size,
      createdAt: attachment.createdAt,
      createdBy: attachment.createdBy,
    };
  }

  /**
   * Tạo presigned URL để upload tệp đính kèm
   * @param tenantId ID tenant (required for tenant isolation)
   * @param currentUserId ID người dùng hiện tại
   * @param dto Dữ liệu tạo upload URL
   * @returns Response chứa presigned URL
   */
  async createUploadUrl(
    tenantId: number,
    currentUserId: number,
    dto: CreateUploadUrlDto,
  ): Promise<UploadUrlResponseDto> {
    try {
      // Kiểm tra công việc tồn tại
      const todo = await this.todoRepository.findById(tenantId, dto.todoId);
      if (!todo) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
          `Không tìm thấy công việc với ID ${dto.todoId}`,
        );
      }

      // Validate file size (max 50MB cho attachments)
      const maxFileSize = FileSizeEnum.FIFTY_MB;
      if (dto.fileSize > maxFileSize) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.ATTACHMENT_CREATION_FAILED,
          `Kích thước file vượt quá giới hạn ${maxFileSize / (1024 * 1024)}MB`,
        );
      }

      // Tạo key cho file (đường dẫn trên S3)
      const timestamp = Date.now();
      const randomSuffix = Math.random().toString(36).substring(2, 8);
      const sanitizedFileName = dto.fileName.replace(/[^a-zA-Z0-9.-]/g, '_');
      const s3Key = `todos/attachments/${dto.todoId}/${timestamp}_${sanitizedFileName}`;

      // Xác định MediaType từ MIME type
      const mediaType = dto.mimeType as MediaType;

      // Tạo presigned URL với thời hạn 15 phút
      const uploadUrl = await this.s3Service.createPresignedWithID(
        s3Key,
        TimeIntervalEnum.FIFTEEN_MINUTES,
        mediaType,
        maxFileSize,
      );

      // Tạo upload ID để track
      const uploadId = `upload_${timestamp}_${randomSuffix}`;

      // Tính thời gian hết hạn
      const expiresAt = Date.now() + (TimeIntervalEnum.FIFTEEN_MINUTES * 1000);

      this.logger.log(`Đã tạo presigned URL cho attachment với upload ID: ${uploadId}`);

      return {
        uploadUrl,
        s3Key,
        uploadId,
        expiresAt,
        maxFileSize,
        acceptedMimeType: dto.mimeType,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo presigned URL: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TODOLISTS_ERROR_CODES.ATTACHMENT_CREATION_FAILED,
        `Không thể tạo URL upload: ${error.message}`,
      );
    }
  }

  /**
   * Xác nhận upload thành công và lưu thông tin vào database
   * @param tenantId ID tenant (required for tenant isolation)
   * @param currentUserId ID người dùng hiện tại
   * @param dto Dữ liệu xác nhận upload
   * @returns Thông tin tệp đính kèm đã lưu
   */
  async confirmUpload(
    tenantId: number,
    currentUserId: number,
    dto: ConfirmUploadDto,
  ): Promise<TodoAttachmentResponseDto> {
    try {
      // Kiểm tra công việc tồn tại
      const todo = await this.todoRepository.findById(tenantId, dto.todoId);
      if (!todo) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
          `Không tìm thấy công việc với ID ${dto.todoId}`,
        );
      }

      // Lưu thông tin tệp đính kèm vào database
      const now = Date.now();
      const attachment = await this.todoAttachmentRepository.create(tenantId, {
        todoId: dto.todoId,
        filename: dto.fileName,
        url: dto.s3Key, // Lưu S3 key thay vì URL
        contentType: dto.contentType,
        size: dto.size,
        createdAt: now,
        createdBy: currentUserId,
      });

      this.logger.log(`Đã xác nhận upload thành công cho attachment ID: ${attachment.id}`);

      return await this.mapToDto(attachment);
    } catch (error) {
      this.logger.error(
        `Lỗi khi xác nhận upload: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TODOLISTS_ERROR_CODES.ATTACHMENT_CREATION_FAILED,
        `Không thể xác nhận upload: ${error.message}`,
      );
    }
  }

  /**
   * Thêm tệp đính kèm cho công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param currentUserId ID người dùng hiện tại
   * @param dto Dữ liệu tệp đính kèm
   * @returns Thông tin tệp đính kèm đã thêm
   */
  async addAttachment(
    tenantId: number,
    currentUserId: number,
    dto: CreateTodoAttachmentDto,
  ): Promise<TodoAttachmentResponseDto> {
    try {
      // Kiểm tra công việc tồn tại
      const todo = await this.todoRepository.findById(tenantId, dto.todoId);
      if (!todo) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
          `Không tìm thấy công việc với ID ${dto.todoId}`,
        );
      }

      // Tạo tệp đính kèm mới
      const now = Date.now();
      const attachment = await this.todoAttachmentRepository.create(tenantId, {
        todoId: dto.todoId,
        filename: dto.filename,
        url: dto.url,
        contentType: dto.contentType,
        size: dto.size,
        createdAt: now,
        createdBy: currentUserId,
      });

      return await this.mapToDto(attachment);
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm tệp đính kèm: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TODOLISTS_ERROR_CODES.ATTACHMENT_CREATION_FAILED,
        'Không thể thêm tệp đính kèm',
      );
    }
  }

  /**
   * Lấy danh sách tệp đính kèm
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Tham số truy vấn
   * @returns Danh sách tệp đính kèm đã phân trang
   */
  async findAll(
    tenantId: number,
    query: TodoAttachmentQueryDto,
  ): Promise<PaginatedResult<TodoAttachmentResponseDto>> {
    try {
      const paginatedResult = await this.todoAttachmentRepository.findAll(
        tenantId,
        query,
      );

      const items = await Promise.all(
        paginatedResult.items.map((attachment) => this.mapToDto(attachment))
      );

      return {
        items,
        meta: paginatedResult.meta,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách tệp đính kèm: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.ATTACHMENT_FETCH_FAILED,
        'Không thể lấy danh sách tệp đính kèm',
      );
    }
  }

  /**
   * Lấy danh sách tệp đính kèm của một công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param todoId ID công việc
   * @returns Danh sách tệp đính kèm
   */
  async findByTodoId(
    tenantId: number,
    todoId: number,
  ): Promise<TodoAttachmentResponseDto[]> {
    try {
      const attachments = await this.todoAttachmentRepository.findByTodoId(
        tenantId,
        todoId,
      );
      return await Promise.all(
        attachments.map((attachment) => this.mapToDto(attachment))
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách tệp đính kèm của công việc: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.ATTACHMENT_FETCH_FAILED,
        'Không thể lấy danh sách tệp đính kèm của công việc',
      );
    }
  }

  /**
   * Lấy chi tiết tệp đính kèm
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID tệp đính kèm
   * @returns Thông tin chi tiết tệp đính kèm
   */
  async findById(
    tenantId: number,
    id: number,
  ): Promise<TodoAttachmentResponseDto> {
    try {
      const attachment = await this.todoAttachmentRepository.findById(
        tenantId,
        id,
      );
      if (!attachment) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.ATTACHMENT_NOT_FOUND,
          `Không tìm thấy tệp đính kèm với ID ${id}`,
        );
      }
      return await this.mapToDto(attachment);
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy chi tiết tệp đính kèm: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TODOLISTS_ERROR_CODES.ATTACHMENT_FETCH_FAILED,
        'Không thể lấy chi tiết tệp đính kèm',
      );
    }
  }

  /**
   * Xóa tệp đính kèm
   * @param tenantId ID tenant (required for tenant isolation)
   * @param currentUserId ID người dùng hiện tại
   * @param id ID tệp đính kèm
   */
  async removeAttachment(
    tenantId: number,
    currentUserId: number,
    id: number,
  ): Promise<void> {
    try {
      // Kiểm tra tệp đính kèm tồn tại
      const attachment = await this.todoAttachmentRepository.findById(
        tenantId,
        id,
      );
      if (!attachment) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.ATTACHMENT_NOT_FOUND,
          `Không tìm thấy tệp đính kèm với ID ${id}`,
        );
      }

      // Kiểm tra quyền xóa tệp đính kèm
      if (attachment.createdBy !== currentUserId) {
        // Kiểm tra xem người dùng có phải là người tạo công việc hoặc người được giao việc không
        const todo = await this.todoRepository.findById(
          tenantId,
          attachment.todoId,
        );
        if (
          !todo ||
          (todo.createdBy !== currentUserId &&
            todo.assigneeId !== currentUserId)
        ) {
          throw new AppException(
            TODOLISTS_ERROR_CODES.NOT_ATTACHMENT_OWNER,
            'Bạn không có quyền xóa tệp đính kèm này',
          );
        }
      }

      // Xóa tệp đính kèm
      await this.todoAttachmentRepository.remove(tenantId, id);

      // TODO: Xóa tệp thực tế trên storage (S3, CloudFlare, ...)
      // Có thể thêm logic xóa tệp thực tế ở đây
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa tệp đính kèm: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TODOLISTS_ERROR_CODES.ATTACHMENT_DELETION_FAILED,
        'Không thể xóa tệp đính kèm',
      );
    }
  }
}
