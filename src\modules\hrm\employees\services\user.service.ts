import { Injectable, Logger } from '@nestjs/common';
import { UserRepository } from '@/modules/auth/repositories/user.repository';
import { EmployeeRepository } from '../repositories/employee.repository';
import { DepartmentRepository } from '../../org-units/repositories/department.repository';
import { UserQueryDto } from '../dto/user-query.dto';
import { UserResponseDto } from '../dto/user-response.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common/exceptions/app.exception';
import { HRM_ERROR_CODES } from '../../errors/hrm-error.code';
import { User } from '@/modules/auth/entities/user.entity';

/**
 * Service xử lý logic nghiệp vụ cho người dùng
 */
@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    private readonly userRepository: UserRepository,
    private readonly employeeRepository: EmployeeRepository,
    private readonly departmentRepository: DepartmentRepository,
  ) {}

  /**
   * Lấy danh sách người dùng với phân trang và lọc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Tham số truy vấn
   * @returns Danh sách người dùng đã phân trang
   */
  async findAllUsers(
    tenantId: number,
    query: UserQueryDto,
  ): Promise<PaginatedResult<UserResponseDto>> {
    try {
      const paginatedResult = await this.userRepository.findAll(tenantId, query);

      // Map users to response DTOs with additional information
      const items = await Promise.all(
        paginatedResult.items.map((user) => this.mapToResponseDto(tenantId, user))
      );

      return {
        items,
        meta: paginatedResult.meta,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách người dùng: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.USER_FETCH_FAILED,
        `Lấy danh sách người dùng thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết người dùng
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID người dùng
   * @returns Thông tin chi tiết người dùng
   */
  async findUserById(tenantId: number, id: number): Promise<UserResponseDto> {
    try {
      const user = await this.userRepository.findById(id);
      if (!user || user.tenantId !== tenantId) {
        throw new AppException(
          HRM_ERROR_CODES.USER_NOT_FOUND,
          `Không tìm thấy người dùng với ID ${id}`,
        );
      }

      return await this.mapToResponseDto(tenantId, user);
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy chi tiết người dùng: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        HRM_ERROR_CODES.USER_FETCH_FAILED,
        `Lấy chi tiết người dùng thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Chuyển đổi User entity sang UserResponseDto
   * @param tenantId ID tenant
   * @param user User entity
   * @returns UserResponseDto
   */
  private async mapToResponseDto(tenantId: number, user: User): Promise<UserResponseDto> {
    let departmentName: string | null = null;
    let employeeCode: string | null = null;
    let employeeName: string | null = null;

    // Lấy thông tin phòng ban nếu có
    if (user.departmentId) {
      try {
        const department = await this.departmentRepository.findById(
          tenantId,
          user.departmentId,
        );
        departmentName = department?.name || null;
      } catch (error) {
        this.logger.warn(`Không thể lấy thông tin phòng ban ${user.departmentId}: ${error.message}`);
      }
    }

    // Lấy thông tin nhân viên nếu có
    if (user.employeeId) {
      try {
        const employee = await this.employeeRepository.findById(
          tenantId,
          user.employeeId,
        );
        if (employee) {
          employeeCode = employee.employeeCode;
          employeeName = employee.employeeName;
        }
      } catch (error) {
        this.logger.warn(`Không thể lấy thông tin nhân viên ${user.employeeId}: ${error.message}`);
      }
    }

    return {
      id: user.id,
      email: user.email,
      fullName: user.fullName,
      employeeId: user.employeeId,
      departmentId: user.departmentId,
      departmentName,
      status: user.status,
      position: user.position,
      phoneNumber: user.phoneNumber,
      address: user.address,
      dateOfBirth: user.dateOfBirth,
      gender: user.gender,
      userType: user.userType,
      createdAt: user.createdAt,
      employeeCode,
      employeeName,
    };
  }
}
